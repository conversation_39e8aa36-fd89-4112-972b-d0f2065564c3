import av
import av.error
import numpy as np
import time
import glob
import os
import traceback
import threading
import queue
import io
import asyncio
import httpx
from typing import List, Optional, Tuple, Union
from live_stream_services import generate_text_from_llm, text_to_speech_wav, load_banned_words

class AudioBuffer:
    """音频缓冲区，负责存储待推送的音频数据"""
    
    def __init__(self, channels: int):
        """
        初始化音频缓冲区
        
        参数:
            channels: 音频通道数
        """
        self.channels = channels
        self.buffer = [np.array([], dtype=np.float32) for _ in range(channels)]
        self.lock = threading.Lock()
    
    def add_samples(self, samples: np.ndarray):
        """
        添加音频样本到缓冲区
        
        参数:
            samples: 音频样本数组，形状为 [channels, samples]
        """
        with self.lock:
            if samples.ndim == 1 and self.channels == 1:
                samples = samples.reshape(1, -1)
            
            if samples.shape[0] != self.channels:
                raise ValueError(f"样本通道数 {samples.shape[0]} 与缓冲区通道数 {self.channels} 不匹配")
            
            for ch_idx in range(self.channels):
                self.buffer[ch_idx] = np.concatenate((self.buffer[ch_idx], samples[ch_idx]))
    
    def get_samples(self, num_samples: int) -> Tuple[np.ndarray, int]:
        """
        从缓冲区获取指定数量的样本
        
        参数:
            num_samples: 请求的样本数量
            
        返回:
            (samples, actual_samples): 样本数组和实际获取的样本数量
        """
        output = np.zeros((self.channels, num_samples), dtype=np.float32)
        actual_samples = 0
        
        with self.lock:
            buffer_has_enough = all(len(ch_buf) >= num_samples for ch_buf in self.buffer)
            
            if buffer_has_enough:
                for ch_idx in range(self.channels):
                    output[ch_idx, :] = self.buffer[ch_idx][:num_samples]
                    self.buffer[ch_idx] = self.buffer[ch_idx][num_samples:]
                actual_samples = num_samples
            else:
                partial_data_exists = any(len(ch_buf) > 0 for ch_buf in self.buffer)
                if partial_data_exists:
                    min_available = min(len(ch_buf) for ch_buf in self.buffer if len(ch_buf) > 0)
                    take_samples = min(min_available, num_samples)
                    
                    for ch_idx in range(self.channels):
                        if len(self.buffer[ch_idx]) >= take_samples:
                            output[ch_idx, :take_samples] = self.buffer[ch_idx][:take_samples]
                            self.buffer[ch_idx] = self.buffer[ch_idx][take_samples:]
                    
                    actual_samples = take_samples
        
        return output, actual_samples
    
    def is_empty(self) -> bool:
        """检查缓冲区是否为空"""
        with self.lock:
            return all(len(ch_buf) == 0 for ch_buf in self.buffer)
    
    def get_buffer_length_seconds(self, sample_rate: int) -> float:
        """
        获取缓冲区中音频数据的时长（秒）
        
        参数:
            sample_rate: 采样率
            
        返回:
            缓冲区音频时长（秒）
        """
        with self.lock:
            if self.channels > 0:
                return len(self.buffer[0]) / sample_rate
            return 0


class RTMPAudioPusher:
    """RTMP音频推流库，支持推送音频文件和实时音频数据"""
    
    def __init__(self, 
                 rtmp_url: str,
                 sample_rate: int = 44100,
                 audio_layout: str = 'mono',  # 默认改为mono，符合当前上下文
                 sample_format: str = 'fltp',
                 audio_bitrate: int = 128000):
        """
        初始化RTMP音频推流器
        
        参数:
            rtmp_url: RTMP服务器URL
            sample_rate: 音频采样率
            audio_layout: 音频声道布局 ('mono', 'stereo'等)
            sample_format: 音频采样格式 (如'fltp'为AAC的浮点平面格式)
            audio_bitrate: 音频比特率
        """
        self.rtmp_url = rtmp_url
        self.sample_rate = sample_rate
        self.audio_layout = audio_layout
        self.sample_format = sample_format
        self.audio_bitrate = audio_bitrate
        
        # 根据音频布局确定通道数
        if audio_layout == 'stereo':
            self.channels = 2
        elif audio_layout == 'mono':
            self.channels = 1
        else:
            try:
                temp_layout_obj = av.AudioLayout(audio_layout)
                self.channels = temp_layout_obj.channels
            except ValueError:
                raise ValueError(f"不支持的音频布局: {audio_layout}")
        
        # 确定AAC帧大小
        temp_codec = av.Codec('aac', 'w')
        temp_context = temp_codec.create()
        temp_context.sample_rate = sample_rate
        temp_context.layout = audio_layout
        temp_context.format = sample_format
        self.frame_size = temp_context.frame_size or 1024
        del temp_context
        del temp_codec
        
        # 初始化缓冲区和线程同步对象
        self.audio_buffer = AudioBuffer(self.channels)
        self.file_queue = queue.Queue()
        self.stop_event = threading.Event()
        
        # 状态变量
        self.rtmp_container = None
        self.audio_stream = None
        self.pts_counter = 0
        self.is_connected = False
        self.loader_thread = None
        self.pusher_thread = None
        self.generator_thread = None # 新增：用于实时生成内容的线程
        self.all_files_loaded_event = threading.Event()
        self.live_mode = False # 新增：直播模式标志
    
    def connect(self) -> bool:
        """
        连接到RTMP服务器
        
        返回:
            连接是否成功
        """
        try:
            self.rtmp_container = av.open(self.rtmp_url, mode='w', format='flv')
            self.audio_stream = self.rtmp_container.add_stream(
                'aac', 
                rate=self.sample_rate, 
                layout=self.audio_layout, 
                format=self.sample_format
            )
            
            if self.audio_bitrate:
                self.audio_stream.bit_rate = self.audio_bitrate
            
            self.is_connected = True
            print(f"已连接到RTMP服务器: {self.rtmp_url}")
            return True
            
        except av.error.AVError as e:
            print(f"连接RTMP服务器失败: {e}")
            return False
        except Exception as e:
            print(f"连接RTMP服务器时发生未知错误: {e}")
            traceback.print_exc()
            return False
    
    def start_silence_stream(self):
        """启动静音流，保持连接不断流"""
        if not self.is_connected:
            if not self.connect():
                raise RuntimeError("无法启动静音流：RTMP连接失败")
        
        self.stop_event.clear()
        self.pusher_thread = threading.Thread(
            target=self._silence_pusher_thread, 
            daemon=True
        )
        self.pusher_thread.start()
        print("已启动静音流，保持RTMP连接")
    
    def _silence_pusher_thread(self):
        """内部方法：推送静音数据的线程"""
        while not self.stop_event.is_set():
            if not self.is_connected:
                try:
                    if not self.connect():
                        time.sleep(1)  # 连接失败，等待后重试
                        continue
                except Exception as e:
                    print(f"重新连接失败: {e}")
                    time.sleep(1)
                    continue
            
            # 生成静音帧
            silence_data = np.zeros((self.channels, self.frame_size), dtype=np.float32)
            silence_frame = av.AudioFrame.from_ndarray(
                silence_data, 
                format=self.sample_format, 
                layout=self.audio_layout
            )
            silence_frame.pts = self.pts_counter
            silence_frame.sample_rate = self.sample_rate
            self.pts_counter += self.frame_size
            
            try:
                # 编码并推送
                packets = self.audio_stream.encode(silence_frame)
                if packets:
                    self.rtmp_container.mux(packets)
            except Exception as e:
                print(f"推送静音帧时出错: {e}")
                self.is_connected = False
                continue
            
            # 控制推流速率
            time.sleep(float(self.frame_size) / self.sample_rate * 0.95)
    
    def add_audio_file(self, file_path: str):
        """
        添加音频文件到播放队列
        
        参数:
            file_path: 音频文件路径
        """
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return
        
        self.file_queue.put(file_path)
        print(f"已添加音频文件到队列: {file_path}")
    
    def add_audio_directory(self, directory_path: str, file_pattern: str = "*.wav"):
        """
        添加目录中的所有音频文件到播放队列
        
        参数:
            directory_path: 音频文件目录
            file_pattern: 文件匹配模式
        """
        files = sorted(glob.glob(os.path.join(directory_path, file_pattern)))
        if not files:
            alt_pattern = "*.mp3" if file_pattern == "*.wav" else "*.wav"
            files = sorted(glob.glob(os.path.join(directory_path, alt_pattern)))
        
        if not files:
            print(f"目录中没有找到匹配的音频文件: {directory_path}")
            return
        
        print(f"从目录添加 {len(files)} 个音频文件到队列")
        for file_path in files:
            self.file_queue.put(file_path)
            
        # 标记所有文件已加入队列
        self.all_files_loaded_event.set()
    
    def add_audio_samples(self, samples: np.ndarray):
        """
        直接添加音频样本数据到缓冲区
        
        参数:
            samples: 音频样本数组，形状为 [channels, samples]
        """
        self.audio_buffer.add_samples(samples)
    
    def start_audio_loader(self):
        """启动音频加载线程，从队列加载音频文件到缓冲区"""
        if self.loader_thread and self.loader_thread.is_alive():
            print("音频加载线程已在运行")
            return
        
        self.stop_event.clear()
        self.loader_thread = threading.Thread(
            target=self._audio_loader_thread,
            daemon=True
        )
        self.loader_thread.start()
        print("已启动音频加载线程")
    
    def _audio_loader_thread(self):
        """内部方法：从队列加载音频文件的线程"""
        resampler = None
        current_input_container = None

        while not self.stop_event.is_set():
            if self.file_queue.empty():
                if self.all_files_loaded_event.is_set():
                    time.sleep(0.1)
                    continue
                else:
                    time.sleep(0.1)
                    continue

            # 简单回压机制 - 缓冲区超过5秒则等待
            buffer_len_seconds = self.audio_buffer.get_buffer_length_seconds(self.sample_rate)
            if buffer_len_seconds > 5:
                time.sleep(0.05)
                continue

            try:
                file_path = self.file_queue.get_nowait()
            except queue.Empty:
                continue

            print(f"加载器: 处理文件 {file_path}")
            try:
                current_input_container = av.open(file_path, 'r')
                input_streams = [s for s in current_input_container.streams if s.type == 'audio']
                if not input_streams:
                    print(f"加载器: 文件中没有音频流 {file_path}. 跳过.")
                    if current_input_container: current_input_container.close()
                    current_input_container = None
                    continue

                input_stream = input_streams[0]
                in_codec_ctx = input_stream.codec_context
                in_fmt_name = in_codec_ctx.format.name if in_codec_ctx.format else "unknown"
                in_layout_obj = in_codec_ctx.layout
                in_layout_name = in_layout_obj.name if in_layout_obj else "unknown"
                in_rate = in_codec_ctx.sample_rate

                if not in_layout_obj or in_layout_obj.channels == 0:
                    if in_codec_ctx.channels == 1: in_layout_name = "mono"
                    elif in_codec_ctx.channels == 2: in_layout_name = "stereo"
                    else: 
                        print(f"加载器: 无法推断布局 {file_path}. 跳过.")
                        continue
                
                resampler = None  # 每个文件重置重采样器
                if (in_fmt_name != self.sample_format or 
                    in_layout_name != self.audio_layout or 
                    in_rate != self.sample_rate):
                    print(f"加载器: 对 {os.path.basename(file_path)} 进行重采样")
                    resampler = av.AudioResampler(
                        format=self.sample_format, 
                        layout=self.audio_layout, 
                        rate=self.sample_rate
                    )

                for frame in current_input_container.decode(streams=input_stream.index):
                    if self.stop_event.is_set(): break
                    frames_to_buffer_list = []
                    if resampler:
                        resampled_output = resampler.resample(frame)
                        if resampled_output: frames_to_buffer_list.extend(resampled_output)
                    else:  # 检查是否需要重新格式化
                        if (frame.format.name != self.sample_format or
                            (frame.layout.name if frame.layout else "") != self.audio_layout or
                             frame.rate != self.sample_rate):
                            try:
                                reformatted = frame.reformat(
                                    format=self.sample_format, 
                                    layout=self.audio_layout, 
                                    rate=self.sample_rate
                                )
                                if reformatted: frames_to_buffer_list.append(reformatted)
                            except Exception as e_ref: 
                                print(f"加载器: 重新格式化失败: {e_ref}")
                        else: 
                            frames_to_buffer_list.append(frame)
                    
                    for f_to_buf in frames_to_buffer_list:
                        if f_to_buf and f_to_buf.samples > 0:
                            np_s = f_to_buf.to_ndarray()
                            if np_s.ndim == 1 and self.channels == 1: 
                                np_s = np_s.reshape(1, -1)
                            if np_s.shape[0] != self.channels: 
                                continue
                            self.audio_buffer.add_samples(np_s)
                
                if resampler:  # 文件结束时刷新重采样器
                    flushed_resampler = resampler.resample(None)
                    if flushed_resampler:
                        for fr_buf in flushed_resampler:
                            if fr_buf and fr_buf.samples > 0:
                                np_s = fr_buf.to_ndarray()
                                if np_s.ndim == 1 and self.channels == 1: 
                                    np_s = np_s.reshape(1, -1)
                                if np_s.shape[0] != self.channels: 
                                    continue
                                self.audio_buffer.add_samples(np_s)
            
            except StopIteration:  # 预期的EOF
                pass
            except av.error.AVError as e_av:
                print(f"加载器: PyAV错误处理 {file_path}: {e_av}")
            except Exception as e:
                print(f"加载器: 通用错误处理 {file_path}: {e}")
                traceback.print_exc()
            finally:
                if current_input_container: 
                    current_input_container.close()
                    current_input_container = None
                resampler = None  # 清除重采样器
                self.file_queue.task_done()  # 标记项目为已处理

        print("音频加载线程结束.")
    
    def start_streaming(self):
        """启动音频流推送"""
        if not self.is_connected:
            if not self.connect():
                raise RuntimeError("无法启动流：RTMP连接失败")
        
        # 确保停止任何现有流
        self.stop_streaming()
        self.stop_event.clear()
        
        # 启动加载线程（如果尚未启动）
        if not self.loader_thread or not self.loader_thread.is_alive():
            self.start_audio_loader()
        
        # 启动推流线程
        self.pusher_thread = threading.Thread(
            target=self._audio_pusher_thread,
            daemon=True
        )
        self.pusher_thread.start()
        print(f"已启动音频推流到 {self.rtmp_url}")
    
    def _audio_pusher_thread(self):
        """内部方法：音频推流线程（高精度时钟版）"""
        last_status_time = time.time()
        start_time = time.monotonic()
        frame_duration = float(self.frame_size) / self.sample_rate

        while not self.stop_event.is_set():
            loop_start_time = time.monotonic()

            # 从缓冲区获取音频样本
            samples_to_send, actual_samples = self.audio_buffer.get_samples(self.frame_size)

            # 如果文件都已处理，队列为空，且缓冲区为空，则结束
            if (self.all_files_loaded_event.is_set() and
                self.file_queue.empty() and
                self.audio_buffer.is_empty() and
                actual_samples == 0):
                print("推流器: 所有文件已处理，队列和缓冲区为空。准备停止。")
                time.sleep(0.5)
                if self.audio_buffer.is_empty():
                    self.stop_event.set()
                    break
            
            # 如果数据不足，用静音填充
            if actual_samples < self.frame_size:
                silence_needed = self.frame_size - actual_samples
                silence_samples = np.zeros((self.channels, silence_needed), dtype=np.float32)
                if actual_samples > 0:
                    # 将静音附加到现有数据后面
                    samples_to_send[:, actual_samples:] = silence_samples
                else:
                    # 完全是静音帧
                    samples_to_send = silence_samples

            # 创建音频帧并设置PTS
            output_audio_frame = av.AudioFrame.from_ndarray(
                samples_to_send,
                format=self.sample_format,
                layout=self.audio_layout
            )
            output_audio_frame.pts = self.pts_counter
            output_audio_frame.sample_rate = self.sample_rate
            self.pts_counter += self.frame_size

            try:
                # 编码并推送
                packets = self.audio_stream.encode(output_audio_frame)
                if packets:
                    self.rtmp_container.mux(packets)
            except av.error.AVError as e_mux:
                print(f"推流器: 复用/编码错误: {e_mux}")
                self.stop_event.set()
                break
            except Exception as e:
                print(f"推流器: 通用复用/编码错误: {e}")
                traceback.print_exc()
                self.stop_event.set()
                break

            # 每5秒输出状态报告
            if time.time() - last_status_time > 5:
                buf_len_approx = self.audio_buffer.get_buffer_length_seconds(self.sample_rate)
                q_size = self.file_queue.qsize()
                print(f"推流状态: 已发送 ~{self.pts_counter / self.sample_rate:.2f}秒. "
                      f"缓冲区: ~{buf_len_approx:.2f}秒. 队列: {q_size} 个文件.")
                last_status_time = time.time()

            # 高精度时钟控制
            elapsed_time = time.monotonic() - loop_start_time
            sleep_time = frame_duration - elapsed_time
            if sleep_time > 0:
                time.sleep(sleep_time)
        
        print("推流器: 主循环结束. 刷新编码器.")
        try:
            packets = self.audio_stream.encode()  # 刷新编码器
            if packets: 
                self.rtmp_container.mux(packets)
        except Exception as e: 
            print(f"推流器: 刷新编码器错误: {e}")
        
        print(f"推流结束. 总共发送音频时长约 {self.pts_counter / self.sample_rate:.2f} 秒.")

    def start_live_streaming(self):
        """启动实时直播推流"""
        if not self.is_connected:
            if not self.connect():
                raise RuntimeError("无法启动直播：RTMP连接失败")

        self.stop_streaming() # 确保停止任何现有流
        self.stop_event.clear()
        self.live_mode = True

        # 启动内容生成线程
        self.generator_thread = threading.Thread(
            target=self._live_stream_generator_thread,
            daemon=True
        )
        self.generator_thread.start()

        # 启动连续推流线程
        self.pusher_thread = threading.Thread(
            target=self._continuous_pusher_thread,
            daemon=True
        )
        self.pusher_thread.start()
        print(f"已启动实时直播推流到 {self.rtmp_url}")

    def _live_stream_generator_thread(self):
        """内部方法：启动并运行异步内容生成器"""
        try:
            asyncio.run(self._async_live_stream_generator())
        except Exception as e:
            print(f"异步生成器主循环中发生错误: {e}")
            traceback.print_exc()
        print("内容生成线程结束。")

    async def _async_live_stream_generator(self):
        """内部方法：实时生成文本和音频的异步线程"""
        banned_words = load_banned_words() # 加载违禁词

        async with httpx.AsyncClient() as client:
            while not self.stop_event.is_set():
                # 缓冲区有足够内容时异步等待
                while self.audio_buffer.get_buffer_length_seconds(self.sample_rate) > 10 and not self.stop_event.is_set():
                    await asyncio.sleep(0.5)

                # 1. 异步生成文本
                full_text = await generate_text_from_llm()
                if not full_text:
                    print("生成器: LLM未能生成文本，30秒后重试...")
                    await asyncio.sleep(30)
                    continue

                # 按换行符分割文本
                text_segments = [segment.strip() for segment in full_text.split('\n') if segment.strip()]

                if not text_segments:
                    print("生成器: LLM返回了空文本，继续下一轮...")
                    continue
                
                # 过滤包含违禁词的句子
                safe_segments = []
                for segment in text_segments:
                    is_safe = True
                    for word in banned_words:
                        if word in segment:
                            print(f"检测到违禁词 '{word}'，已删除句子: '{segment}'")
                            is_safe = False
                            break
                    if is_safe:
                        safe_segments.append(segment)

                for segment in safe_segments:
                    if self.stop_event.is_set():
                        break
                    
                    # 2. 异步文本转语音
                    print(f"生成器: 正在处理分段: '{segment}'")
                    wav_data = await text_to_speech_wav(segment, client)
                    if not wav_data:
                        print(f"生成器: TTS未能为分段 '{segment}' 生成音频，跳过...")
                        continue
                    
                    # 3. 将WAV数据解码并添加到缓冲区 (这部分仍然是CPU密集型，保持同步执行)
                    try:
                        with io.BytesIO(wav_data) as wav_file:
                            with av.open(wav_file, 'r') as container:
                                input_stream = container.streams.audio[0]
                                resampler = av.AudioResampler(
                                    format=self.sample_format,
                                    layout=self.audio_layout,
                                    rate=self.sample_rate,
                                )
                                for frame in container.decode(audio=0):
                                    for resampled_frame in resampler.resample(frame):
                                        self.audio_buffer.add_samples(resampled_frame.to_ndarray())
                                for flushed_frame in resampler.resample(None):
                                    self.audio_buffer.add_samples(flushed_frame.to_ndarray())
                        print(f"生成器: 成功添加了 '{segment[:20]}...' 的音频。")
                    except Exception as e:
                        print(f"生成器: 处理分段 '{segment}' 的WAV数据时出错: {e}")
                        traceback.print_exc()

                # 每轮LLM调用之间稍作停顿
                await asyncio.sleep(5)

    def _continuous_pusher_thread(self):
        """内部方法：连续推流线程，无数据时推静音"""
        last_status_time = time.time()

        while not self.stop_event.is_set():
            samples_to_send, actual_samples = self.audio_buffer.get_samples(self.frame_size)

            if actual_samples < self.frame_size:
                # 缓冲区数据不足，用静音填充剩余部分
                silence_needed = self.frame_size - actual_samples
                silence_samples = np.zeros((self.channels, silence_needed), dtype=np.float32)
                if actual_samples > 0:
                    samples_to_send[:, actual_samples:] = silence_samples
                else:
                    samples_to_send = np.zeros((self.channels, self.frame_size), dtype=np.float32)

            # 创建音频帧
            output_audio_frame = av.AudioFrame.from_ndarray(
                samples_to_send,
                format=self.sample_format,
                layout=self.audio_layout
            )
            output_audio_frame.pts = self.pts_counter
            output_audio_frame.sample_rate = self.sample_rate
            self.pts_counter += self.frame_size

            try:
                packets = self.audio_stream.encode(output_audio_frame)
                if packets:
                    self.rtmp_container.mux(packets)
            except Exception as e:
                print(f"连续推流器: 编码/复用错误: {e}")
                self.stop_event.set()
                break
            
            # 状态报告
            if time.time() - last_status_time > 5:
                buf_len_approx = self.audio_buffer.get_buffer_length_seconds(self.sample_rate)
                print(f"直播推流状态: 已发送 ~{self.pts_counter / self.sample_rate:.2f}秒. "
                      f"缓冲区: ~{buf_len_approx:.2f}秒.")
                last_status_time = time.time()

            # 控制速率
            time.sleep(float(self.frame_size) / self.sample_rate * 0.95)

        print("连续推流器: 主循环结束. 刷新编码器.")
        try:
            packets = self.audio_stream.encode()
            if packets:
                self.rtmp_container.mux(packets)
        except Exception as e:
            print(f"连续推流器: 刷新编码器错误: {e}")
    
    def stop_streaming(self):
        """停止音频流"""
        self.stop_event.set()
        
        if self.pusher_thread and self.pusher_thread.is_alive():
            self.pusher_thread.join(timeout=5.0)
            if self.pusher_thread.is_alive():
                print("推流器线程未在指定时间内结束")
        
        if self.loader_thread and self.loader_thread.is_alive():
            self.loader_thread.join(timeout=5.0)
            if self.loader_thread.is_alive():
                print("加载器线程未在指定时间内结束")

        if self.generator_thread and self.generator_thread.is_alive():
            self.generator_thread.join(timeout=5.0)
            if self.generator_thread.is_alive():
                print("生成器线程未在指定时间内结束")
        
        print("音频流已停止")
    
    def get_remaining_time(self) -> float:
        """
        获取剩余播放时间（秒）
        
        返回:
            剩余播放时间，包括缓冲区和队列中的音频
        """
        buffer_time = self.audio_buffer.get_buffer_length_seconds(self.sample_rate)
        
        # 估计队列中文件的时长
        queue_time = 0
        if not self.file_queue.empty():
            # 这里只是粗略估计，实际需要更复杂的逻辑来计算每个文件的时长
            queue_time = self.file_queue.qsize() * 30  # 假设每个文件平均30秒
        
        return buffer_time + queue_time
    
    def close(self):
        """关闭RTMP连接并清理资源"""
        self.stop_streaming()
        
        if self.rtmp_container:
            try:
                self.rtmp_container.close()
                print("RTMP容器已关闭")
            except Exception as e:
                print(f"关闭RTMP容器时出错: {e}")
        
        self.is_connected = False
        self.rtmp_container = None
        self.audio_stream = None 