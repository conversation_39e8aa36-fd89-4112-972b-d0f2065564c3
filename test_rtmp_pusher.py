import time
import numpy as np
from rtmp_audio_pusher import RTMPAudioPusher
from config import RTMP_CONFIG

def test_silence_streaming():
    """测试静音流，保持RTMP连接不断流"""
    pusher = RTMPAudioPusher(rtmp_url='rtmp://localhost/live/mystream')
    
    try:
        # 启动静音流
        pusher.start_silence_stream()
        print("已启动静音流，将持续30秒...")
        time.sleep(30)
    finally:
        # 停止并清理
        pusher.close()
        print("静音流测试完成")

def test_file_streaming():
    """测试音频文件推流"""
    pusher = RTMPAudioPusher(rtmp_url='rtmp://localhost/live/mystream')
    
    try:
        # 添加音频目录
        pusher.add_audio_directory('./voice/')
        
        # 启动流
        pusher.start_streaming()
        
        # 监控剩余时间
        while not pusher.stop_event.is_set():
            remaining_time = pusher.get_remaining_time()
            print(f"剩余播放时间: {remaining_time:.2f} 秒")
            time.sleep(5)
            
            # 如果剩余时间很短且没有更多文件，退出循环
            if remaining_time < 1 and pusher.file_queue.empty():
                break
    finally:
        # 停止并清理
        pusher.close()
        print("文件推流测试完成")

def test_realtime_samples():
    """测试实时音频样本推流"""
    pusher = RTMPAudioPusher(rtmp_url='rtmp://localhost/live/mystream')
    
    try:
        # 连接到RTMP服务器
        if not pusher.connect():
            print("连接失败，测试终止")
            return
        
        # 启动推流
        pusher.start_streaming()
        
        # 生成并推送10秒的正弦波
        sample_rate = pusher.sample_rate
        duration = 10  # 10秒
        
        for i in range(0, duration):
            # 生成1秒的正弦波
            t = np.arange(0, 1.0, 1.0/sample_rate)
            freq = 440 * (i % 3 + 1)  # 变化的频率
            samples = np.sin(2 * np.pi * freq * t).astype(np.float32)
            
            if pusher.channels == 2:
                # 创建立体声信号
                stereo_samples = np.vstack((samples, samples))
                pusher.add_audio_samples(stereo_samples)
            else:
                # 创建单声道信号
                mono_samples = samples.reshape(1, -1)
                pusher.add_audio_samples(mono_samples)
            
            print(f"已添加 {i+1} 秒正弦波 ({freq} Hz)")
            time.sleep(0.5)  # 等待一点时间，避免缓冲过多
        
        # 等待所有样本播放完毕
        while not pusher.audio_buffer.is_empty():
            remaining_time = pusher.get_remaining_time()
            print(f"等待缓冲区清空，剩余: {remaining_time:.2f} 秒")
            time.sleep(0.5)
            if remaining_time < 0.1:
                break
    finally:
        # 停止并清理
        pusher.close()
        print("实时样本推流测试完成")

def test_live_streaming():
    """测试由LLM和TTS驱动的实时直播流"""
    # 注意：请确保已正确填写 config.yaml 文件
    rtmp_url = RTMP_CONFIG.get('url')
    if not rtmp_url:
        print("错误：未在config.yaml中配置RTMP推流地址（rtmp.url）。")
        return
        
    pusher = RTMPAudioPusher(rtmp_url=rtmp_url)
    
    try:
        pusher.start_live_streaming() # prompt从文件加载
        
        print("已启动无限时长的实时直播流...")
        print("请检查您的RTMP播放器（如VLC）以收听直播。")
        print("按 Ctrl+C 停止直播。")
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n检测到用户中断，准备停止直播...")
    except Exception as e:
        print(f"直播测试期间发生错误: {e}")
    finally:
        print("正在停止直播流...")
        pusher.close()
        print("直播流测试完成。")

if __name__ == '__main__':
    # print("=== 测试1: 静音流 ===")
    # test_silence_streaming()
    
    # print("\n=== 测试2: 文件推流 ===")
    # test_file_streaming()
    
    # print("\n=== 测试3: 实时样本推流 ===")
    # test_realtime_samples()

    print("\n=== 测试4: 实时AI直播推流 ===")
    test_live_streaming()