import openai
import httpx
import json
import asyncio
from typing import Optional, Set
from config import TTS_CONFIG, AI_WRITING_CONFIG, AI_FILES_CONFIG

def _read_prompt_file(file_path: str, description: str) -> str:
    """读取提示词文件内容的辅助函数。"""
    if not file_path:
        print(f"错误：未在config.yaml中配置 {description} 文件路径。")
        return ""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        print(f"错误：{description} 文件 '{file_path}' 未找到。")
        return ""
    except Exception as e:
        print(f"读取 {description} 文件时出错: {e}")
        return ""

def load_banned_words(file_path: str = "违禁词.txt") -> Set[str]:
    """
    从文件加载违禁词列表。
    文件应为UTF-8编码，词语由空格分隔。
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            words = set(content.split())
            print(f"成功加载 {len(words)} 个违禁词。")
            return words
    except FileNotFoundError:
        print(f"警告：违禁词文件 '{file_path}' 未找到，将不进行过滤。")
        return set()
    except Exception as e:
        print(f"加载违禁词文件时出错: {e}")
        return set()

# --- Initialize OpenAI client from config ---
async_openai_client = openai.AsyncOpenAI(
    api_key=AI_WRITING_CONFIG.get("api_key"),
    base_url=AI_WRITING_CONFIG.get("base_url"),
)

# 加载一次prompt文件
SYSTEM_PROMPT = _read_prompt_file(AI_FILES_CONFIG.get('prompt'), "System Prompt")
USER_PROMPT_TEMPLATE = _read_prompt_file(AI_FILES_CONFIG.get('source'), "User Prompt")


async def generate_text_from_llm(max_tokens: int = 10240) -> Optional[str]:
    """
    使用OpenAI的LLM生成文本 (异步版本)。
    System和User prompt从配置文件指定的文件中读取。

    参数:
        max_tokens: 生成文本的最大长度。

    返回:
        生成的文本，如果失败则返回None。
    """
    model = AI_WRITING_CONFIG.get("model")
    if not all([async_openai_client.api_key, async_openai_client.base_url, model, SYSTEM_PROMPT, USER_PROMPT_TEMPLATE]):
        print("错误：AI Writing配置或Prompt文件不完整。请检查config.yaml和相关文件。")
        return None
        
    try:
        print(f"正在异步调用LLM生成文本...")
        response = await async_openai_client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": USER_PROMPT_TEMPLATE}
            ],
            max_tokens=max_tokens,
            temperature=0.8,
        )
        text = response.choices[0].message.content.strip()
        print(f"LLM异步生成文本成功: '{text[:50]}...'")
        return text
    except Exception as e:
        print(f"异步调用LLM时出错: {e}")
        return None

async def text_to_speech_wav(text: str, client: httpx.AsyncClient) -> Optional[bytes]:
    """
    异步调用TTS接口将文本转换为WAV音频数据。

    参数:
        text: 要转换的文本。
        client: httpx.AsyncClient 实例。

    返回:
        WAV格式的音频数据（字节），如果失败则返回None。
    """
    tts_url = TTS_CONFIG.get("api_url")
    character = TTS_CONFIG.get("character")
    min_text_length = TTS_CONFIG.get("min_text_length", 0)

    if not tts_url or not character:
        print("错误：TTS配置不完整（api_url, character）。请检查config.yaml。")
        return None

    if len(text) < min_text_length:
        print(f"文本长度 {len(text)} 小于最小要求 {min_text_length}，跳过TTS。")
        return None

    headers = {
        "accept": "*/*",
        "content-type": "application/json",
    }
    body = {
        "text": text,
        "character": character
    }
    
    try:
        print(f"正在异步调用TTS接口，文本: '{text}...'")
        response = await client.post(tts_url, headers=headers, json=body, timeout=20)
        response.raise_for_status()
        
        if response.headers.get('Content-Type') == 'audio/wav':
            print("TTS音频数据异步接收成功。")
            return response.content
        else:
            # 使用 atext() 或 aread() 来异步读取响应体
            response_text = await response.atext()
            print(f"TTS接口返回了非预期的内容类型: {response.headers.get('Content-Type')}")
            print(f"响应内容: {response_text}")
            return None
            
    except httpx.RequestError as e:
        print(f"异步调用TTS接口时出错: {e}")
        return None
    except Exception as e:
        print(f"处理TTS异步响应时发生未知错误: {e}")
        return None

async def main_test():
    """异步测试函数"""
    print("--- 测试LLM文本生成 (异步) ---")
    generated_text = await generate_text_from_llm()
    
    if generated_text:
        print("\n--- 测试TTS语音合成 (异步) ---")
        async with httpx.AsyncClient() as client:
            wav_data = await text_to_speech_wav(generated_text, client)
            if wav_data:
                output_filename = "test_output_async.wav"
                with open(output_filename, "wb") as f:
                    f.write(wav_data)
                print(f"测试音频已保存到 {output_filename}")
    else:
        print("\n由于LLM文本生成失败，跳过TTS测试。")

if __name__ == '__main__':
    # --- 异步测试代码 ---
    # 需要先安装: pip install pyyaml httpx openai
    asyncio.run(main_test())
