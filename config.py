import yaml
import sys

def load_config(path: str = 'config.yaml'):
    """加载YAML配置文件。"""
    try:
        with open(path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            if not config:
                print(f"错误：配置文件 '{path}' 为空或格式不正确。")
                sys.exit(1)
            return config
    except FileNotFoundError:
        print(f"错误：配置文件 '{path}' 未找到。请确保文件存在于同级目录。")
        sys.exit(1)
    except yaml.YAMLError as e:
        print(f"解析配置文件 '{path}' 时出错: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"加载配置文件时发生未知错误: {e}")
        sys.exit(1)

# 在模块导入时加载一次配置
_config = load_config()

# 提供对配置节的便捷访问
TTS_CONFIG = _config.get('tts', {})
_ai_writing_config = _config.get('ai_writing', {})
AI_WRITING_CONFIG = _ai_writing_config.get('openai', {})
AI_FILES_CONFIG = _ai_writing_config.get('files', {})
RTMP_CONFIG = _config.get('rtmp', {})


# 检查关键配置是否存在
if not TTS_CONFIG:
    print("警告：配置文件中缺少 'tts' 部分。")
if not AI_WRITING_CONFIG or not AI_FILES_CONFIG:
    print("警告：配置文件中缺少 'ai_writing.openai' 或 'ai_writing.files' 部分。")
if not RTMP_CONFIG:
    print("警告：配置文件中缺少 'rtmp' 部分。")

if __name__ == '__main__':
    # 用于测试目的
    print("--- TTS Config ---")
    print(TTS_CONFIG)
    print("\n--- AI Writing OpenAI Config ---")
    print(AI_WRITING_CONFIG)
    print("\n--- AI Writing Files Config ---")
    print(AI_FILES_CONFIG)
    print("\n--- RTMP Config ---")
    print(RTMP_CONFIG)